/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.hy.userAgenda.mapper;

import org.springblade.modules.hy.userAgenda.pojo.entity.UserAgendaEntity;
import org.springblade.modules.hy.userAgenda.pojo.vo.UserAgendaVO;
import org.springblade.modules.hy.userAgenda.excel.UserAgendaExcel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 用户会议关联表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
public interface UserAgendaMapper extends BaseMapper<UserAgendaEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page 分页参数
	 * @param userAgenda 查询参数
	 * @return List<UserAgendaVO>
	 */
	List<UserAgendaVO> selectUserAgendaPage(IPage page, UserAgendaVO userAgenda);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper 查询条件
	 * @return List<UserAgendaExcel>
	 */
	List<UserAgendaExcel> exportUserAgenda(@Param("ew") Wrapper<UserAgendaEntity> queryWrapper);

}
