/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.hy.hotel.excel;


import lombok.Data;

import java.util.Date;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;
import java.io.Serial;


/**
 * 酒店信息表 Excel实体类
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class HotelExcel implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 主键，自增
	 */
	@ColumnWidth(20)
	@ExcelProperty("主键，自增")
	private Long id;
	/**
	 * 酒店名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("酒店名称")
	private String hotelName;
	/**
	 * 酒店星级
	 */
	@ColumnWidth(20)
	@ExcelProperty("酒店星级")
	private Integer hotelRating;
	/**
	 * 酒店地址
	 */
	@ColumnWidth(20)
	@ExcelProperty("酒店地址")
	private String hotelAddress;
	/**
	 * 距离会场
	 */
	@ColumnWidth(20)
	@ExcelProperty("距离会场")
	private String distanceToVenue;
	/**
	 * 酒店电话
	 */
	@ColumnWidth(20)
	@ExcelProperty("酒店电话")
	private String hotelPhone;
	/**
	 * 房间类型
	 */
	@ColumnWidth(20)
	@ExcelProperty("房间类型")
	private String roomType;
	/**
	 * 房间设施（JSON格式）
	 */
	@ColumnWidth(20)
	@ExcelProperty("房间设施（JSON格式）")
	private String roomFeatures;
	/**
	 * 入住时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("入住时间")
	private Date checkinTime;
	/**
	 * 退房时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("退房时间")
	private Date checkoutTime;
	/**
	 * 住宿天数
	 */
	@ColumnWidth(20)
	@ExcelProperty("住宿天数")
	private Integer stayDays;
	/**
	 * 酒店服务（JSON格式）
	 */
	@ColumnWidth(20)
	@ExcelProperty("酒店服务（JSON格式）")
	private String hotelServices;
	/**
	 * 交通信息（JSON格式）
	 */
	@ColumnWidth(20)
	@ExcelProperty("交通信息（JSON格式）")
	private String transportInfo;
	/**
	 * 联系方式（JSON格式）
	 */
	@ColumnWidth(20)
	@ExcelProperty("联系方式（JSON格式）")
	private String contactInfo;
	/**
	 * 酒店提示（JSON格式）
	 */
	@ColumnWidth(20)
	@ExcelProperty("酒店提示（JSON格式）")
	private String hotelTips;
	/**
	 * 是否删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否删除")
	private Integer isDeleted;

}
