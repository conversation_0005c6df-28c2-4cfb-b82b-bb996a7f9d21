/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.hy.checkin.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 二维码响应 DTO
 *
 * <AUTHOR>
 * @since 2025-08-08
 */
@Data
@Schema(description = "二维码响应对象")
public class QRCodeResponseDTO implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 二维码图片数据（Base64编码）
	 */
	@Schema(description = "二维码图片数据（Base64编码）")
	private String qrCodeData;

	/**
	 * 签到码
	 */
	@Schema(description = "签到码")
	private String checkinCode;

	/**
	 * 过期时间
	 */
	@Schema(description = "过期时间")
	private LocalDateTime expireTime;

	/**
	 * 用户ID
	 */
	@Schema(description = "用户ID")
	private Long userId;

	/**
	 * 议程ID
	 */
	@Schema(description = "议程ID")
	private Long agendaId;

}
