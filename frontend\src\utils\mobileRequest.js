/**
 * 移动端专用HTTP请求工具
 * 用于处理钉钉登录后的API请求
 */
import request from '@/axios'
import { useUserStore } from '@/store/mobile/user'

/**
 * 移动端认证请求方法
 * @param {Object} config - axios请求配置
 * @returns {Promise} 请求结果
 */
export async function mobileRequest(config) {
  const userStore = useUserStore()
  
  if (!userStore.accessToken) {
    throw new Error('用户未登录')
  }

  const requestConfig = {
    ...config,
    headers: {
      'Blade-Auth': `Bearer ${userStore.accessToken}`,
      'Content-Type': 'application/json',
      'Tenant-Id': '000000',
      'Blade-Requested-With': 'BladeHttpRequest',
      ...config.headers
    }
  }

  try {
    const response = await request(requestConfig)
    return response
  } catch (error) {
    // 如果是401错误，尝试刷新token
    if (error.response && error.response.status === 401) {
      try {
        await userStore.refreshAccessToken()
        // 重新设置token并重试请求
        requestConfig.headers['Blade-Auth'] = `Bearer ${userStore.accessToken}`
        return await request(requestConfig)
      } catch (refreshError) {
        console.error('刷新token失败:', refreshError)
        throw new Error('登录已过期，请重新登录')
      }
    }
    throw error
  }
}

/**
 * 检查用户登录状态
 * @returns {boolean} 是否已登录
 */
export function checkMobileAuth() {
  const userStore = useUserStore()
  return userStore.isLogin && userStore.accessToken
}

/**
 * 获取当前用户信息
 * @returns {Object|null} 用户信息
 */
export function getCurrentUser() {
  const userStore = useUserStore()
  return userStore.userInfo
}

export default {
  mobileRequest,
  checkMobileAuth,
  getCurrentUser
}
