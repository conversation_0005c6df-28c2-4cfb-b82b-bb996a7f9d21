/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.hy.checkin.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 参会签到记录表 实体类
 *
 * <AUTHOR>
 * @since 2025-08-08
 */
@Data
@TableName("hy_attendance_record")
@Schema(description = "AttendanceRecord对象")
@EqualsAndHashCode(callSuper = true)
public class AttendanceRecordEntity extends BaseEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 用户ID
	 */
	@Schema(description = "用户ID")
	private Long userId;

	/**
	 * 议程ID
	 */
	@Schema(description = "议程ID")
	private Long hyAgendaId;

	/**
	 * 签到时间
	 */
	@Schema(description = "签到时间")
	private LocalDateTime checkinTime;

	/**
	 * 签到状态
	 */
	@Schema(description = "签到状态")
	private String statusText;

}
