<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.hy.checkin.mapper.AttendanceRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="attendanceRecordResultMap" type="org.springblade.modules.hy.checkin.pojo.entity.AttendanceRecordEntity">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="hy_agenda_id" property="hyAgendaId"/>
        <result column="checkin_time" property="checkinTime"/>
        <result column="status_text" property="statusText"/>
        <result column="checkin_type" property="checkinType"/>
        <result column="checkin_code" property="checkinCode"/>
        <result column="device_info" property="deviceInfo"/>
        <result column="location" property="location"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="baseColumnList">
        id, user_id, hy_agenda_id, checkin_time, status_text, checkin_type, checkin_code, 
        device_info, location, create_user, create_dept, create_time, update_user, update_time, status, is_deleted
    </sql>

    <!-- 自定义分页查询 -->
    <select id="selectAttendanceRecordPage" resultType="org.springblade.modules.hy.checkin.pojo.vo.AttendanceRecordVO">
        SELECT 
            ar.id,
            ar.user_id,
            ar.hy_agenda_id,
            ar.checkin_time,
            ar.status_text,
            ar.checkin_type,
            ar.checkin_code,
            ar.device_info,
            ar.location,
            ar.create_user,
            ar.create_dept,
            ar.create_time,
            ar.update_user,
            ar.update_time,
            ar.status,
            ar.is_deleted,
            up.name as user_name,
            ag.topic as agenda_topic,
            ag.venue as venue
        FROM hy_attendance_record ar
        LEFT JOIN hy_user_profile up ON ar.user_id = up.id
        LEFT JOIN hy_agenda ag ON ar.hy_agenda_id = ag.id
        WHERE ar.is_deleted = 0
        <if test="attendanceRecord.userId != null">
            AND ar.user_id = #{attendanceRecord.userId}
        </if>
        <if test="attendanceRecord.hyAgendaId != null">
            AND ar.hy_agenda_id = #{attendanceRecord.hyAgendaId}
        </if>
        <if test="attendanceRecord.checkinType != null and attendanceRecord.checkinType != ''">
            AND ar.checkin_type = #{attendanceRecord.checkinType}
        </if>
        <if test="attendanceRecord.statusText != null and attendanceRecord.statusText != ''">
            AND ar.status_text = #{attendanceRecord.statusText}
        </if>
        ORDER BY ar.checkin_time DESC
    </select>

    <!-- 根据用户ID查询今日签到记录 -->
    <select id="selectTodayRecordByUserId" resultMap="attendanceRecordResultMap">
        SELECT <include refid="baseColumnList"/>
        FROM hy_attendance_record
        WHERE user_id = #{userId}
          AND is_deleted = 0
          AND DATE(checkin_time) = CURRENT_DATE
        ORDER BY checkin_time DESC
        LIMIT 1
    </select>

    <!-- 根据用户ID和议程ID查询签到记录 -->
    <select id="selectByUserIdAndAgendaId" resultMap="attendanceRecordResultMap">
        SELECT <include refid="baseColumnList"/>
        FROM hy_attendance_record
        WHERE user_id = #{userId}
          AND hy_agenda_id = #{agendaId}
          AND is_deleted = 0
        ORDER BY checkin_time DESC
        LIMIT 1
    </select>

</mapper>
