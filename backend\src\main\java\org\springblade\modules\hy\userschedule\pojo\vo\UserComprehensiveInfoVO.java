/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.hy.userschedule.pojo.vo;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springblade.modules.hy.hotel.pojo.entity.HotelEntity;
import org.springblade.modules.hy.userAgenda.pojo.entity.UserAgendaEntity;
import org.springblade.modules.hy.dinner.pojo.entity.DinnerEntity;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 用户综合信息 视图实体类
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Data
@Schema(description = "UserComprehensiveInfoVO对象")
public class UserComprehensiveInfoVO implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 用户日程信息
	 */
	@Schema(description = "用户日程信息")
	private UserScheduleVO userSchedule;

	/**
	 * 酒店信息
	 */
	@Schema(description = "酒店信息")
	private HotelEntity hotel;

	/**
	 * 用户议程列表
	 */
	@Schema(description = "用户议程列表")
	private List<UserAgendaEntity> agendaList;

	/**
	 * 用户用餐记录列表
	 */
	@Schema(description = "用户用餐记录列表")
	private List<DinnerEntity> dinnerList;

}
