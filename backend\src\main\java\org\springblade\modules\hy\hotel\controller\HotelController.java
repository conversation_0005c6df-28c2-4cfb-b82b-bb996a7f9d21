/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.hy.hotel.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.hy.hotel.pojo.entity.HotelEntity;
import org.springblade.modules.hy.hotel.pojo.vo.HotelVO;
import org.springblade.modules.hy.hotel.excel.HotelExcel;
import org.springblade.modules.hy.hotel.wrapper.HotelWrapper;
import org.springblade.modules.hy.hotel.service.IHotelService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 酒店信息表 控制器
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
@RestController
@AllArgsConstructor
@RequestMapping("hy/hotel")
@Tag(name = "酒店信息表", description = "酒店信息表接口")
public class HotelController extends BladeController {

	private final IHotelService hotelService;

	/**
	 * 酒店信息表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入hotel")
	public R<HotelVO> detail(HotelEntity hotel) {
		HotelEntity detail = hotelService.getOne(Condition.getQueryWrapper(hotel));
		return R.data(HotelWrapper.build().entityVO(detail));
	}
	/**
	 * 酒店信息表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入hotel")
	public R<IPage<HotelVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> hotel, Query query) {
		IPage<HotelEntity> pages = hotelService.page(Condition.getPage(query), Condition.getQueryWrapper(hotel, HotelEntity.class));
		return R.data(HotelWrapper.build().pageVO(pages));
	}

	/**
	 * 酒店信息表 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入hotel")
	public R<IPage<HotelVO>> page(HotelVO hotel, Query query) {
		IPage<HotelVO> pages = hotelService.selectHotelPage(Condition.getPage(query), hotel);
		return R.data(pages);
	}

	/**
	 * 酒店信息表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入hotel")
	public R save(@Valid @RequestBody HotelEntity hotel) {
		return R.status(hotelService.save(hotel));
	}

	/**
	 * 酒店信息表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入hotel")
	public R update(@Valid @RequestBody HotelEntity hotel) {
		return R.status(hotelService.updateById(hotel));
	}

	/**
	 * 酒店信息表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入hotel")
	public R submit(@Valid @RequestBody HotelEntity hotel) {
		return R.status(hotelService.saveOrUpdate(hotel));
	}

	/**
	 * 酒店信息表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(hotelService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@GetMapping("/export-hotel")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入hotel")
	public void exportHotel(@Parameter(hidden = true) @RequestParam Map<String, Object> hotel, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<HotelEntity> queryWrapper = Condition.getQueryWrapper(hotel, HotelEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(Hotel::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(HotelEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<HotelExcel> list = hotelService.exportHotel(queryWrapper);
		ExcelUtil.export(response, "酒店信息表数据" + DateUtil.time(), "酒店信息表数据表", list, HotelExcel.class);
	}

}
