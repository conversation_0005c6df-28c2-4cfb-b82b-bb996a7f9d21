<template>
  <div class="page-container">
    <main class="page-content">
      <div class="list-container">
        <div class="form-header">
          <i class="fas fa-calendar-check"></i>
          <h2>{{ mainTitle || "我的日程" }}</h2>
          <p>{{ subTitle || "个人会议安排" }}</p>
        </div>

        <!-- 可滚动的内容容器 -->
        <div class="scrollable-content">

          <!-- 注意事项 -->
          <div class="guide-content">
            <div class="guide-section">
              <h3><i class="fas fa-exclamation-triangle"></i> 重要提醒</h3>
              <div class="rules-list">
                <div v-for="notice in importantNotices" :key="notice.title" class="rule-item">
                  <i class="fas fa-exclamation-circle"></i>
                  <div>
                    <strong>{{ notice.title }}</strong>
                    <p>{{ notice.content }}</p>
                  </div>
                </div>
              </div>
            </div>

            <div class="guide-section">
              <h3><i class="fas fa-phone"></i> 紧急联系</h3>
              <div class="contact-emergency">
                <div v-for="contact in emergencyContacts" :key="contact.title" class="emergency-item">
                  <strong>{{ contact.title }}</strong>
                  <span>{{ contact.contact }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

      </div>
    </main>
  </div>
</template>

<script>
import {getList as getAgendaList} from '@/api/agenda/agenda';
import {dataTransformers} from '@/utils/apiHelper';
import {getDictionary} from '@/api/system/dictbiz'

export default {
  name: 'Guide',
  data() {
    return {
      mainTitle: '',
      subTitle: '',
      agendaList: [],
      importantNotices: [
        {title: '着装要求', content: '建议商务正装，体现专业形象'},
        {title: '准时参会', content: '请提前15分钟到达会场，避免迟到影响会议进程'},
        {title: '携带证件', content: '请携带身份证或工作证，用于会议签到'}
      ],
      emergencyContacts: [
        {title: '会务组联系人：', contact: '张先生 138-0000-0000'},
        {title: '技术支持：', contact: '李女士 139-0000-0000'},
        {title: '医疗急救：', contact: '120'}
      ]
    }
  },

  computed: {
    groupedAgenda() {
      const grouped = {};

      this.agendaList.forEach(item => {
        const date = item.date || '2025-09-15';
        if (!grouped[date]) grouped[date] = [];
        grouped[date].push(item);
      });

      Object.keys(grouped).forEach(date => {
        grouped[date].sort((a, b) => {
          const timeA = this.extractStartTime(a.time);
          const timeB = this.extractStartTime(b.time);
          return timeA.localeCompare(timeB);
        });
      });

      const sortedDates = Object.keys(grouped).sort((a, b) =>
          new Date(a).getTime() - new Date(b).getTime()
      );

      const sortedGrouped = {};
      sortedDates.forEach(date => sortedGrouped[date] = grouped[date]);

      return sortedGrouped;
    }
  },

  async mounted() {
    try {
      await this.loadAgendaData();
      await this.loadTitleData();
    } catch (error) {
      console.error('Guide组件挂载过程中出错:', error);
    }
  },

  methods: {
    async loadTitleData() {
      try {
        const response = await getDictionary({code: 'hy_guide'});
        if (response?.data?.success && response.data.data?.length) {
          const dictData = response.data.data;

          this.mainTitle = dictData.find(item => item.dictValue === '主标题')?.dictKey;
          this.subTitle = dictData.find(item => item.dictValue === '副标题')?.dictKey;
          this.extractImportantNotices(dictData);
          this.extractEmergencyContacts(dictData);
        }
      } catch (error) {
        console.error('加载标题数据失败:', error);
      }
    },

    extractImportantNotices(dictData) {
      try {
        const noticeParentItem = dictData.find(dict =>
            dict.dictKey === '重要提醒' || dict.dictValue === '重要提醒'
        );

        if (noticeParentItem) {
          const noticeItems = dictData
              .filter(dict => dict.parentId === noticeParentItem.id)
              .map(item => ({
                title: item.dictValue,
                content: item.dictKey
              }));

          if (noticeItems.length) this.importantNotices = noticeItems;
        }
      } catch (error) {
        console.error('提取重要提醒失败:', error);
      }
    },

    extractEmergencyContacts(dictData) {
      try {
        const contactParentItem = dictData.find(dict =>
            dict.dictKey === '紧急联系' || dict.dictValue === '紧急联系'
        );

        if (contactParentItem) {
          const contactItems = dictData
              .filter(dict => dict.parentId === contactParentItem.id)
              .map(item => ({
                title: item.dictValue,
                contact: item.dictKey
              }));

          if (contactItems.length) this.emergencyContacts = contactItems;
        }
      } catch (error) {
        console.error('提取紧急联系失败:', error);
      }
    },

    async loadAgendaData() {
      try {
        this.agendaList = this.getDefaultAgendaData();
        try {
          const response = await getAgendaList(1, 50, {});
          if (response?.data?.success) {
            const transformedData = dataTransformers.agenda(response.data);
            if (transformedData?.length) this.agendaList = transformedData;
          }
        } catch (apiError) {
          console.warn('议程API调用失败，继续使用默认数据:', apiError);
        }
      } catch (error) {
        console.error('加载议程数据失败:', error);
        this.agendaList = this.getDefaultAgendaData();
      }
    },

    getDefaultAgendaData() {
      return [
        {id: 1, time: '08:30-09:00', topic: '签到注册', date: '2025-09-15'},
        {id: 2, time: '09:00-09:30', topic: '开幕式', date: '2025-09-15'},
        {id: 3, time: '09:30-10:30', topic: '主题演讲：数智攀登，管理跃升', date: '2025-09-15'},
        {id: 4, time: '10:30-10:45', topic: '茶歇', date: '2025-09-15'},
        {id: 5, time: '10:45-12:00', topic: '圆桌讨论：企业管理创新实践', date: '2025-09-15'},
        {id: 6, time: '12:00-13:30', topic: '午餐时间', date: '2025-09-15'},
        {id: 7, time: '09:00-10:00', topic: '经验分享：数字化管理实践', date: '2025-09-16'},
        {id: 8, time: '10:00-11:00', topic: '现场参观', date: '2025-09-16'},
        {id: 9, time: '11:00-12:00', topic: '闭幕式', date: '2025-09-16'}
      ];
    },

    extractStartTime(timeStr) {
      if (!timeStr) return '00:00:00';
      const startTime = timeStr.split('-')[0].trim();
      return startTime.length === 5 ? startTime + ':00' : startTime || '00:00:00';
    },

    formatDate(dateStr) {
      if (!dateStr) return '会议议程';
      try {
        const date = new Date(dateStr);
        const month = date.getMonth() + 1;
        const day = date.getDate();
        const allDates = [...new Set(this.agendaList.map(item => item.date))].sort();
        const dayIndex = allDates.indexOf(dateStr);

        return dayIndex >= 0 ? `第${dayIndex + 1}天 - ${month}月${day}日` : `${month}月${day}日`;
      } catch (error) {
        return dateStr;
      }
    },

    downloadGuide() {
      if (this.$message) {
        this.$message.success('参会指南已准备好！\n（这是一个演示功能）');
      } else {
        alert('参会指南已准备好！\n（这是一个演示功能）');
      }
    }
  }
}
</script>

<style scoped>
/* 页面通用样式 */
.page-container {
  width: 100%;
  max-height: 85vh;
  background-size: cover;
  position: relative;
  z-index: 1;
  margin: 0 auto;
  box-sizing: border-box;
}

.page-content {
  margin-top: 15px;
}

/* 容器样式 - 统一磨砂玻璃效果 */
.list-container {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(12px);
  border-radius: 15px;
  padding: 10px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: inset 0 0 10px rgba(255, 255, 255, 0.05),
  0 10px 25px rgba(0, 0, 0, 0.3);
  animation: slideInUp 0.6s ease forwards;
  max-width: 1400px;
  margin: 0 auto;
  max-height: 85vh;
  display: flex;
  flex-direction: column;
}

/* 添加可滚动内容容器样式 */
.scrollable-content {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding-right: 5px;
  max-height: calc(85vh - 200px);
}

/* 滚动条美化 */
.scrollable-content::-webkit-scrollbar {
  width: 5px;
}

.scrollable-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
}

.scrollable-content::-webkit-scrollbar-thumb {
  background: rgba(7, 211, 240, 0.5);
  border-radius: 10px;
}

.scrollable-content::-webkit-scrollbar-thumb:hover {
  background: rgba(7, 211, 240, 0.8);
}

/* 标题区域样式统一 */
.form-header {
  text-align: center;
  margin-bottom: 25px;
  color: #ffffff;
}

.form-header i {
  font-size: 36px;
  color: #07D3F0;
  margin-bottom: 10px;
  text-shadow: 0 0 15px rgba(7, 211, 240, 0.6);
}

.form-header h2 {
  color: #ffffff;
  font-size: 24px;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.form-header p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  padding: 0 10px;
}

/* 内容区域样式 */
.guide-content {
  animation: fadeIn 0.3s ease;
}

.guide-section {
  margin-bottom: 25px;
}

.guide-section h3 {
  background: linear-gradient(135deg, rgba(7, 211, 240, 0.3), rgba(7, 211, 240, 0.15));
  color: white;
  padding: 15px 20px;
  border-radius: 10px;
  margin-bottom: 20px;
  font-size: 17px;
  display: flex;
  align-items: center;
  gap: 10px;
  border: 1px solid rgba(7, 211, 240, 0.2);
  backdrop-filter: blur(8px);
}

.guide-section h3 i {
  color: #ffffff;
  text-shadow: 0 0 10px rgba(7, 211, 240, 0.5);
}

/* 日程安排样式 */
.schedule-day {
  margin-bottom: 20px;
}

.schedule-day h4 {
  color: #07D3F0;
  font-size: 14px;
  margin-bottom: 10px;
  padding: 8px 12px;
  background: rgba(7, 211, 240, 0.1);
  border-radius: 8px;
}

.schedule-list {
  background: rgba(255, 255, 255, 0.06);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  padding: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.schedule-list::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(120deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0) 100%);
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
}

.schedule-list:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.schedule-item {
  display: flex;
  padding: 10px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  position: relative;
  z-index: 2;
}

.schedule-item:last-child {
  border-bottom: none;
}

.schedule-item .time {
  color: #07D3F0;
  font-size: 12px;
  font-weight: 500;
  min-width: 80px;
}

.schedule-item .content {
  color: rgba(255, 255, 255, 0.9);
  font-size: 13px;
  flex: 1;
}

.schedule-item .topic {
  margin-left: 5px;
  font-weight: 500;
  margin-bottom: 4px;
  color: rgba(255, 255, 255, 0.9);
}

/* 无数据样式 */
.no-data {
  text-align: center;
  padding: 40px 20px;
  color: rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.06);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.no-data i {
  font-size: 48px;
  color: rgba(7, 211, 240, 0.3);
  margin-bottom: 15px;
}

.no-data p {
  font-size: 14px;
  margin: 0;
}

/* 会议须知样式 */
.rules-list {
  background: rgba(255, 255, 255, 0.06);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  padding: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.rules-list::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(120deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0) 100%);
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
}

.rules-list:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.rule-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 15px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  position: relative;
  z-index: 2;
}

.rule-item:last-child {
  border-bottom: none;
}

.rule-item i {
  color: #07D3F0;
  font-size: 18px;
  margin-top: 2px;
}

.rule-item strong {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  display: block;
  margin-bottom: 3px;
}

.rule-item p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  margin: 0;
}

/* 紧急联系样式 */
.contact-emergency {
  background: rgba(255, 255, 255, 0.06);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-left: 4px solid rgba(7, 211, 240, 0.7);
  position: relative;
  overflow: hidden;
}

.contact-emergency::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(120deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0) 100%);
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
}

.contact-emergency:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.contact-emergency:hover {
  transform: translateX(3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  background: rgba(255, 255, 255, 0.09);
}

.emergency-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  position: relative;
  z-index: 2;
}

.emergency-item:last-child {
  border-bottom: none;
}

.emergency-item strong {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
}

.emergency-item span {
  color: #07D3F0;
  font-size: 14px;
  font-weight: 500;
}

/* 下载按钮样式 */
.download-section {
  text-align: center;
  margin-top: auto;
  padding: 15px 0;
}

.submit-btn {
  width: 100%;
  background: linear-gradient(135deg, rgba(7, 211, 240, 0.3), rgba(7, 211, 240, 0.15));
  color: white;
  border: 1px solid rgba(7, 211, 240, 0.3);
  border-radius: 10px;
  padding: 14px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  backdrop-filter: blur(8px);
  position: relative;
  overflow: hidden;
}

.submit-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(120deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0) 100%);
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
}

.submit-btn:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(7, 211, 240, 0.2);
  background: linear-gradient(135deg, rgba(7, 211, 240, 0.4), rgba(7, 211, 240, 0.2));
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .list-container {
    padding: 15px;
  }
}
</style>