export default {
  height:'auto',
  calcHeight: 30,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "主键，自增",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "酒店名称",
      prop: "hotelName",
      type: "input",
      search: true,
    },
    {
      label: "酒店星级",
      prop: "hotelRating",
      type: "input",
    },
    {
      label: "酒店地址",
      prop: "hotelAddress",
      type: "input",
      search: true,
    },
    {
      label: "距离会场",
      prop: "distanceToVenue",
      type: "input",
    },
    {
      label: "酒店电话",
      prop: "hotelPhone",
      type: "input",
      search: true,
    },
    {
      label: "房间类型",
      prop: "roomType",
      type: "input",
    },
    {
      label: "房间设施（JSON格式）",
      prop: "roomFeatures",
      type: "input",
    },
    {
      label: "入住时间",
      prop: "checkinTime",
      type: "date",
      format: "YYYY-MM-DD HH:mm:ss",
      valueFormat: "YYYY-MM-DD HH:mm:ss",
    },
    {
      label: "退房时间",
      prop: "checkoutTime",
      type: "date",
      format: "YYYY-MM-DD HH:mm:ss",
      valueFormat: "YYYY-MM-DD HH:mm:ss",
    },
    {
      label: "住宿天数",
      prop: "stayDays",
      type: "input",
    },
    {
      label: "酒店服务（JSON格式）",
      prop: "hotelServices",
      type: "textarea",
    },
    {
      label: "交通信息（JSON格式）",
      prop: "transportInfo",
      type: "textarea",
    },
    {
      label: "联系方式（JSON格式）",
      prop: "contactInfo",
      type: "textarea",
    },
    {
      label: "酒店提示（JSON格式）",
      prop: "hotelTips",
      type: "textarea",
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "更新人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "更新时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
