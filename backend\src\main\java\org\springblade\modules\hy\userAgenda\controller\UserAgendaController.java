/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.hy.userAgenda.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.hy.userAgenda.pojo.entity.UserAgendaEntity;
import org.springblade.modules.hy.userAgenda.pojo.vo.UserAgendaVO;
import org.springblade.modules.hy.userAgenda.excel.UserAgendaExcel;
import org.springblade.modules.hy.userAgenda.wrapper.UserAgendaWrapper;
import org.springblade.modules.hy.userAgenda.service.IUserAgendaService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 用户会议关联表 控制器
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@RestController
@AllArgsConstructor
@RequestMapping("hy/userAgenda")
@Tag(name = "用户会议关联表", description = "用户会议关联表接口")
public class UserAgendaController extends BladeController {

	private final IUserAgendaService userAgendaService;

	/**
	 * 用户会议关联表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入userAgenda")
	public R<UserAgendaVO> detail(UserAgendaEntity userAgenda) {
		UserAgendaEntity detail = userAgendaService.getOne(Condition.getQueryWrapper(userAgenda));
		return R.data(UserAgendaWrapper.build().entityVO(detail));
	}
	/**
	 * 用户会议关联表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入userAgenda")
	public R<IPage<UserAgendaVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> userAgenda, Query query) {
		IPage<UserAgendaEntity> pages = userAgendaService.page(Condition.getPage(query), Condition.getQueryWrapper(userAgenda, UserAgendaEntity.class));
		return R.data(UserAgendaWrapper.build().pageVO(pages));
	}

	/**
	 * 用户会议关联表 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入userAgenda")
	public R<IPage<UserAgendaVO>> page(UserAgendaVO userAgenda, Query query) {
		IPage<UserAgendaVO> pages = userAgendaService.selectUserAgendaPage(Condition.getPage(query), userAgenda);
		return R.data(pages);
	}

	/**
	 * 用户会议关联表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入userAgenda")
	public R save(@Valid @RequestBody UserAgendaEntity userAgenda) {
		return R.status(userAgendaService.save(userAgenda));
	}

	/**
	 * 用户会议关联表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入userAgenda")
	public R update(@Valid @RequestBody UserAgendaEntity userAgenda) {
		return R.status(userAgendaService.updateById(userAgenda));
	}

	/**
	 * 用户会议关联表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入userAgenda")
	public R submit(@Valid @RequestBody UserAgendaEntity userAgenda) {
		return R.status(userAgendaService.saveOrUpdate(userAgenda));
	}

	/**
	 * 用户会议关联表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(userAgendaService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@GetMapping("/export-userAgenda")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入userAgenda")
	public void exportUserAgenda(@Parameter(hidden = true) @RequestParam Map<String, Object> userAgenda, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<UserAgendaEntity> queryWrapper = Condition.getQueryWrapper(userAgenda, UserAgendaEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(UserAgenda::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(UserAgendaEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<UserAgendaExcel> list = userAgendaService.exportUserAgenda(queryWrapper);
		ExcelUtil.export(response, "用户会议关联表数据" + DateUtil.time(), "用户会议关联表数据表", list, UserAgendaExcel.class);
	}

}
