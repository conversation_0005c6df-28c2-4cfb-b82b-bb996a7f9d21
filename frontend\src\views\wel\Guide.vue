<template>
  <div class="page-container">
    <main class="page-content">
      <div class="list-container">
        <div class="form-header">
          <i class="fas fa-book-open"></i>
          <h2>{{ mainTitle || "参会指南" }}</h2>
          <p>{{ subTitle }}</p>
        </div>

        <!-- 指南分类 -->
        <div class="guide-categories">
          <button
              class="guide-tab"
              :class="{ active: activeTab === 'basic' }"
              @click="switchTab('basic')"
          >
            基本信息
          </button>
          <button
              class="guide-tab"
              :class="{ active: activeTab === 'schedule' }"
              @click="switchTab('schedule')"
          >
            日程安排
          </button>
          <button
              class="guide-tab"
              :class="{ active: activeTab === 'location' }"
              @click="switchTab('location')"
          >
            交通住宿
          </button>
          <button
              class="guide-tab"
              :class="{ active: activeTab === 'rules' }"
              @click="switchTab('rules')"
          >
            注意事项
          </button>
        </div>

        <!-- 可滚动的内容容器 -->
        <div class="scrollable-content">
          <!-- 基本信息 -->
          <div class="guide-content" v-show="activeTab === 'basic'">
            <div class="guide-section">
              <h3><i class="fas fa-info-circle"></i> 会议基本信息</h3>
              <div class="info-card">
                <div class="info-row">
                  <strong>会议名称：</strong>
                  <span>{{ meetingInfo.meetingName }}</span>
                </div>
                <div class="info-row">
                  <strong>主办单位：</strong>
                  <span>{{ meetingInfo.organizer }}</span>
                </div>
                <div class="info-row">
                  <strong>会议时间：</strong>
                  <span>{{ meetingInfo.meetingTime }}</span>
                </div>
                <div class="info-row">
                  <strong>会议地点：</strong>
                  <span>{{ meetingInfo.meetingLocation }}</span>
                </div>
                <div class="info-row">
                  <strong>参会人数：</strong>
                  <span>{{ meetingInfo.participantCount }}</span>
                </div>
              </div>
            </div>

            <div class="guide-section">
              <h3><i class="fas fa-users"></i> 参会对象</h3>
              <div class="participant-list">
                <div v-for="participant in participants" :key="participant.name" class="participant-item">
                  <i :class="participant.icon"></i>
                  <span>{{ participant.name }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 日程安排 -->
          <div class="guide-content" v-show="activeTab === 'schedule'">
            <div class="guide-section">
              <h3><i class="fas fa-calendar-alt"></i> 详细日程</h3>

              <!-- 动态渲染议程数据 -->
              <div v-if="agendaList && agendaList.length > 0">
                <!-- 按日期分组显示 -->
                <div v-for="(dayAgenda, date) in groupedAgenda" :key="date" class="schedule-day">
                  <h4>{{ formatDate(date) }}</h4>
                  <div class="schedule-list">
                    <div v-for="agenda in dayAgenda" :key="agenda.id" class="schedule-item">
                      <div class="time">{{ agenda.time }}</div>
                      <div class="content">
                        <div class="topic">{{ agenda.topic }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 无数据时显示默认内容 -->
              <div v-else class="no-data">
                <i class="fas fa-calendar-times"></i>
                <p>暂无议程数据</p>
              </div>
            </div>
          </div>

          <!-- 交通住宿 -->
          <div class="guide-content" v-show="activeTab === 'location'">
            <div class="guide-section">
              <h3><i class="fas fa-map-marker-alt"></i> 会议地点</h3>
              <div class="location-card">
                <div class="location-info">
                  <h4>广东烟草大厦会议中心</h4>
                  <p>地址：广州市天河区珠江新城</p>
                  <p>电话：020-12345678</p>
                </div>
                <button class="map-btn" @click="openMap">
                  <i class="fas fa-map"></i>
                  查看地图
                </button>
              </div>
            </div>

            <div class="guide-section">
              <h3><i class="fas fa-car"></i> 交通指南</h3>
              <div class="transport-options">
                <div v-for="transport in transportInfo" :key="transport.type" class="transport-item">
                  <i :class="transport.icon"></i>
                  <div>
                    <strong>{{ transport.type }}</strong>
                    <p>{{ transport.description }}</p>
                  </div>
                </div>
              </div>
            </div>

            <div class="guide-section">
              <h3><i class="fas fa-bed"></i> 住宿推荐</h3>
              <div class="hotel-list">
                <div v-for="hotel in hotelList" :key="hotel.name" class="hotel-item">
                  <h4>{{ hotel.name }}</h4>
                  <p>{{ hotel.description }}</p>
                  <p v-if="hotel.phone">联系电话：{{ hotel.phone }}</p>
                </div>
              </div>
            </div>
          </div>

          <!-- 注意事项 -->
          <div class="guide-content" v-show="activeTab === 'rules'">
            <div class="guide-section">
              <h3><i class="fas fa-exclamation-triangle"></i> 重要提醒</h3>
              <div class="rules-list">
                <div v-for="notice in importantNotices" :key="notice.title" class="rule-item">
                  <i class="fas fa-exclamation-circle"></i>
                  <div>
                    <strong>{{ notice.title }}</strong>
                    <p>{{ notice.content }}</p>
                  </div>
                </div>
              </div>
            </div>

            <div class="guide-section">
              <h3><i class="fas fa-phone"></i> 紧急联系</h3>
              <div class="contact-emergency">
                <div v-for="contact in emergencyContacts" :key="contact.title" class="emergency-item">
                  <strong>{{ contact.title }}</strong>
                  <span>{{ contact.contact }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 下载指南 -->
        <div class="download-section">
          <button class="submit-btn" @click="downloadGuide">
            <i class="fas fa-download"></i>
            下载完整指南
          </button>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import { getList } from '@/api/guide/guide';
import { getList as getAgendaList } from '@/api/agenda/agenda';
import { dataTransformers } from '@/utils/apiHelper';
import { getDictionary } from '@/api/system/dictbiz'

export default {
  name: 'Guide',
  data() {
    return {
      mainTitle: '',
      subTitle: '',
      activeTab: 'basic',
      guides: [],
      agendaList: [],
      dataSource: 'unknown',
      responseTime: 0,
      defaultGuidesData: [],
      meetingInfo: {
        meetingName: '数智攀登，管理跃升 - 企业管理现场会',
        organizer: '广东烟草广州市有限公司',
        meetingTime: '2025年9月15日-16日',
        meetingLocation: '广东烟草大厦会议中心',
        participantCount: '约120人'
      },
      participants: [
        { icon: 'fas fa-crown', name: '公司领导层' },
        { icon: 'fas fa-user-tie', name: '各部门负责人' },
        { icon: 'fas fa-users', name: '管理人员' },
        { icon: 'fas fa-star', name: '特邀嘉宾' }
      ],
      transportInfo: [
        { icon: 'fas fa-subway', type: '地铁', description: '3号线/5号线珠江新城站，A出口步行5分钟' },
        { icon: 'fas fa-bus', type: '公交', description: '多路公交车可达，珠江新城站下车' },
        { icon: 'fas fa-car', type: '自驾', description: '大厦地下停车场，收费标准：10元/小时' }
      ],
      hotelList: [
        { name: '广州大酒店', description: '距离会场：步行3分钟', phone: '020-87654321' },
        { name: '珠江宾馆', description: '距离会场：步行8分钟', phone: '020-87654322' }
      ],
      importantNotices: [
        { title: '着装要求', content: '建议商务正装，体现专业形象' },
        { title: '准时参会', content: '请提前15分钟到达会场，避免迟到影响会议进程' },
        { title: '携带证件', content: '请携带身份证或工作证，用于会议签到' }
      ],
      emergencyContacts: [
        { title: '会务组联系人：', contact: '张先生 138-0000-0000' },
        { title: '技术支持：', contact: '李女士 139-0000-0000' },
        { title: '医疗急救：', contact: '120' }
      ]
    }
  },

  computed: {
    groupedAgenda() {
      const grouped = {};

      this.agendaList.forEach(item => {
        const date = item.date || '2025-09-15';
        if (!grouped[date]) grouped[date] = [];
        grouped[date].push(item);
      });

      Object.keys(grouped).forEach(date => {
        grouped[date].sort((a, b) => {
          const timeA = this.extractStartTime(a.time);
          const timeB = this.extractStartTime(b.time);
          return timeA.localeCompare(timeB);
        });
      });

      const sortedDates = Object.keys(grouped).sort((a, b) =>
          new Date(a).getTime() - new Date(b).getTime()
      );

      const sortedGrouped = {};
      sortedDates.forEach(date => sortedGrouped[date] = grouped[date]);

      return sortedGrouped;
    }
  },

  async mounted() {
    this.defaultGuidesData = [...this.guides];
    try {
      await this.loadGuidesData();
      await this.loadAgendaData();
      await this.loadData();
    } catch (error) {
      console.error('Guide组件挂载过程中出错:', error);
    }
  },

  methods: {
    async loadData() {
      try {
        await this.loadTitleData();
        await this.loadMeetingInfoData();
      } catch (error) {
        console.error('加载数据失败:', error);
        this.mainTitle = '参会指南';
        this.subTitle = '企业管理现场会参会须知';
      }
    },

    async loadTitleData() {
      try {
        const response = await getDictionary({ code: 'hy_guide' });
        if (response?.data?.success && response.data.data?.length) {
          const dictData = response.data.data;

          this.mainTitle = dictData.find(item => item.dictValue === '主标题')?.dictKey;
          this.subTitle = dictData.find(item => item.dictValue === '副标题')?.dictKey;

          this.extractParticipants(dictData);
          this.extractTransportInfo(dictData);
          this.extractHotelInfo(dictData);
          this.extractImportantNotices(dictData);
          this.extractEmergencyContacts(dictData);
        }
      } catch (error) {
        console.error('加载标题数据失败:', error);
      }
    },

    async loadMeetingInfoData() {
      try {
        const response = await getDictionary({ code: 'hy_prop' });
        if (response?.data?.success && response.data.data?.length) {
          this.extractMeetingInfo(response.data.data);
        }
      } catch (error) {
        console.error('加载会议信息失败:', error);
      }
    },

    extractParticipants(dictData) {
      try {
        const participantMappings = [
          { dictKey: '1', icon: 'fas fa-crown', order: 2 },
          { dictKey: '2', icon: 'fas fa-user-tie', order: 3 },
          { dictKey: '3', icon: 'fas fa-users', order: 4 },
          { dictKey: '4', icon: 'fas fa-star', order: 5 }
        ];

        const participantItems = participantMappings
            .map(mapping => {
              const item = dictData.find(dict => dict.dictKey === mapping.dictKey);
              return item?.dictValue ? { ...mapping, name: item.dictValue } : null;
            })
            .filter(Boolean)
            .sort((a, b) => a.order - b.order);

        if (participantItems.length) this.participants = participantItems;
      } catch (error) {
        console.error('提取参会对象失败:', error);
      }
    },

    extractTransportInfo(dictData) {
      try {
        const transportMappings = [
          { dictValue: '地铁', icon: 'fas fa-subway', type: '地铁' },
          { dictValue: '公交', icon: 'fas fa-bus', type: '公交' },
          { dictValue: '自驾', icon: 'fas fa-car', type: '自驾' }
        ];

        const transportItems = transportMappings
            .map(mapping => {
              const item = dictData.find(dict => dict.dictValue === mapping.dictValue);
              return item ? { ...mapping, description: item.dictKey } : null;
            })
            .filter(Boolean);

        if (transportItems.length) this.transportInfo = transportItems;
      } catch (error) {
        console.error('提取交通信息失败:', error);
      }
    },

    extractHotelInfo(dictData) {
      try {
        const hotelParentItem = dictData.find(dict =>
            dict.dictKey === '住宿推荐' || dict.dictValue === '住宿推荐'
        );

        if (hotelParentItem) {
          const hotelItems = dictData
              .filter(dict => dict.parentId === hotelParentItem.id)
              .map(item => ({
                name: item.dictValue,
                description: item.dictKey,
                phone: item.remark || ''
              }));

          if (hotelItems.length) this.hotelList = hotelItems;
        }
      } catch (error) {
        console.error('提取住宿推荐失败:', error);
      }
    },

    extractImportantNotices(dictData) {
      try {
        const noticeParentItem = dictData.find(dict =>
            dict.dictKey === '重要提醒' || dict.dictValue === '重要提醒'
        );

        if (noticeParentItem) {
          const noticeItems = dictData
              .filter(dict => dict.parentId === noticeParentItem.id)
              .map(item => ({
                title: item.dictValue,
                content: item.dictKey
              }));

          if (noticeItems.length) this.importantNotices = noticeItems;
        }
      } catch (error) {
        console.error('提取重要提醒失败:', error);
      }
    },

    extractEmergencyContacts(dictData) {
      try {
        const contactParentItem = dictData.find(dict =>
            dict.dictKey === '紧急联系' || dict.dictValue === '紧急联系'
        );

        if (contactParentItem) {
          const contactItems = dictData
              .filter(dict => dict.parentId === contactParentItem.id)
              .map(item => ({
                title: item.dictValue,
                contact: item.dictKey
              }));

          if (contactItems.length) this.emergencyContacts = contactItems;
        }
      } catch (error) {
        console.error('提取紧急联系失败:', error);
      }
    },

    extractMeetingInfo(dictData) {
      try {
        const meetingLocationItem = dictData.find(item => item.dictKey === '会议地点');
        if (meetingLocationItem) this.meetingInfo.meetingLocation = meetingLocationItem.dictValue;

        const meetingNameItem = dictData.find(item => item.dictKey === '会议主标题');
        if (meetingNameItem) this.meetingInfo.meetingName = meetingNameItem.dictValue;

        const organizerItem = dictData.find(item => item.dictKey === '主办单位');
        if (organizerItem) this.meetingInfo.organizer = organizerItem.dictValue;

        const meetingTimeItem = dictData.find(item => item.dictKey === '会议时间');
        if (meetingTimeItem) this.meetingInfo.meetingTime = meetingTimeItem.dictValue;

        const participantCountItem = dictData.find(item => item.dictKey === '参会人数');
        if (participantCountItem) this.meetingInfo.participantCount = participantCountItem.dictValue;
      } catch (error) {
        console.error('提取会议信息失败:', error);
      }
    },

    async loadGuidesData() {
      const startTime = Date.now();
      try {
        const response = await getList(1, 20, {});
        if (response?.data?.success) {
          const transformedData = dataTransformers.guides(response.data);
          if (transformedData?.length) this.guides = transformedData;
          this.dataSource = 'api';
          this.hasError = false;
          this.errorMessage = '';
        } else {
          throw new Error('API响应格式不正确');
        }
        this.responseTime = Date.now() - startTime;
      } catch (error) {
        console.error('加载指南数据失败:', error);
        this.guides = this.defaultGuidesData;
        this.dataSource = 'fallback';
        this.hasError = true;
        this.errorMessage = error.message || '数据加载失败，使用默认数据';
        this.responseTime = Date.now() - startTime;
      }
    },

    async loadAgendaData() {
      try {
        this.agendaList = this.getDefaultAgendaData();
        try {
          const response = await getAgendaList(1, 50, {});
          if (response?.data?.success) {
            const transformedData = dataTransformers.agenda(response.data);
            if (transformedData?.length) this.agendaList = transformedData;
          }
        } catch (apiError) {
          console.warn('议程API调用失败，继续使用默认数据:', apiError);
        }
      } catch (error) {
        console.error('加载议程数据失败:', error);
        this.agendaList = this.getDefaultAgendaData();
      }
    },

    getDefaultAgendaData() {
      return [
        { id: 1, time: '08:30-09:00', topic: '签到注册', speaker: '会务组', venue: '主会场大厅', description: '参会者签到，领取会议资料', date: '2025-09-15' },
        { id: 2, time: '09:00-09:30', topic: '开幕式', speaker: '主办方领导', venue: '主会场', description: '大会开幕致辞，嘉宾介绍', date: '2025-09-15' },
        { id: 3, time: '09:30-10:30', topic: '主题演讲：数智攀登，管理跃升', speaker: '公司领导', venue: '主会场', description: '探讨数字化智能化在企业管理中的创新应用', date: '2025-09-15' },
        { id: 4, time: '10:30-10:45', topic: '茶歇', speaker: '', venue: '休息区', description: '休息时间，自由交流', date: '2025-09-15' },
        { id: 5, time: '10:45-12:00', topic: '圆桌讨论：企业管理创新实践', speaker: '各部门负责人', venue: '主会场', description: '管理层共同探讨企业管理现代化转型路径', date: '2025-09-15' },
        { id: 6, time: '12:00-13:30', topic: '午餐时间', speaker: '', venue: '餐厅', description: '自助午餐，网络交流', date: '2025-09-15' },
        { id: 7, time: '09:00-10:00', topic: '经验分享：数字化管理实践', speaker: '技术专家', venue: '主会场', description: '分享数字化转型的实践经验', date: '2025-09-16' },
        { id: 8, time: '10:00-11:00', topic: '现场参观', speaker: '导览员', venue: '生产车间', description: '参观智能化生产线', date: '2025-09-16' },
        { id: 9, time: '11:00-12:00', topic: '闭幕式', speaker: '主办方领导', venue: '主会场', description: '大会总结，颁奖典礼', date: '2025-09-16' }
      ];
    },

    extractStartTime(timeStr) {
      if (!timeStr) return '00:00:00';
      const startTime = timeStr.split('-')[0].trim();
      return startTime.length === 5 ? startTime + ':00' : startTime || '00:00:00';
    },

    formatDate(dateStr) {
      if (!dateStr) return '会议议程';
      try {
        const date = new Date(dateStr);
        const month = date.getMonth() + 1;
        const day = date.getDate();
        const allDates = [...new Set(this.agendaList.map(item => item.date))].sort();
        const dayIndex = allDates.indexOf(dateStr);

        return dayIndex >= 0 ? `第${dayIndex + 1}天 - ${month}月${day}日` : `${month}月${day}日`;
      } catch (error) {
        return dateStr;
      }
    },

    switchTab(tabName) {
      this.activeTab = tabName;
    },

    openMap() {
      if (this.$message) {
        this.$message.info('地图导航功能');
      } else {
        alert('地图导航\n（演示功能）\n将打开地图应用导航到会议地点');
      }
    },

    downloadGuide() {
      if (this.$message) {
        this.$message.success('参会指南已准备好！\n（这是一个演示功能）');
      } else {
        alert('参会指南已准备好！\n（这是一个演示功能）');
      }
    }
  }
}
</script>

<style scoped>
/* 页面通用样式 */
.page-container {
  width: 100%;
  max-height: 85vh;
  background-size: cover;
  position: relative;
  z-index: 1;
  margin: 0 auto;
  box-sizing: border-box;
}

.page-content {
  margin-top: 15px;
}

/* 容器样式 - 统一磨砂玻璃效果 */
.list-container {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(12px);
  border-radius: 15px;
  padding: 10px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: inset 0 0 10px rgba(255, 255, 255, 0.05),
  0 10px 25px rgba(0, 0, 0, 0.3);
  animation: slideInUp 0.6s ease forwards;
  max-width: 1400px;
  margin: 0 auto;
  max-height: 85vh;
  display: flex;
  flex-direction: column;
}

/* 添加可滚动内容容器样式 */
.scrollable-content {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding-right: 5px;
  max-height: calc(85vh - 300px);
}

/* 滚动条美化 */
.scrollable-content::-webkit-scrollbar {
  width: 5px;
}

.scrollable-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
}

.scrollable-content::-webkit-scrollbar-thumb {
  background: rgba(7, 211, 240, 0.5);
  border-radius: 10px;
}

.scrollable-content::-webkit-scrollbar-thumb:hover {
  background: rgba(7, 211, 240, 0.8);
}

/* 标题区域样式统一 */
.form-header {
  text-align: center;
  margin-bottom: 25px;
  color: #ffffff;
}

.form-header i {
  font-size: 36px;
  color: #07D3F0;
  margin-bottom: 10px;
  text-shadow: 0 0 15px rgba(7, 211, 240, 0.6);
}

.form-header h2 {
  color: #ffffff;
  font-size: 24px;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.form-header p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  padding: 0 10px;
}

/* TAB导航样式 */
.guide-categories {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 0 20px;
  padding: 5px 0;
  width: 100%;
  flex-wrap: nowrap;
}

.guide-tab {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(7, 211, 240, 0.3);
  color: #07D3F0;
  padding: 8px 8px;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}

.guide-tab::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(120deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
}

.guide-tab:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.guide-tab.active,
.guide-tab:hover {
  background: rgba(7, 211, 240, 0.2);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(7, 211, 240, 0.2);
}

/* 内容区域样式 */
.guide-content {
  animation: fadeIn 0.3s ease;
}

.guide-section {
  margin-bottom: 25px;
}

.guide-section h3 {
  background: linear-gradient(135deg, rgba(7, 211, 240, 0.3), rgba(7, 211, 240, 0.15));
  color: white;
  padding: 15px 20px;
  border-radius: 10px;
  margin-bottom: 20px;
  font-size: 17px;
  display: flex;
  align-items: center;
  gap: 10px;
  border: 1px solid rgba(7, 211, 240, 0.2);
  backdrop-filter: blur(8px);
}

.guide-section h3 i {
  color: #ffffff;
  text-shadow: 0 0 10px rgba(7, 211, 240, 0.5);
}

/* 基本信息样式 */
.info-card {
  background: rgba(255, 255, 255, 0.06);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-left: 4px solid rgba(7, 211, 240, 0.7);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.info-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(120deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
}

.info-card:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.info-card:hover {
  transform: translateX(3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  background: rgba(255, 255, 255, 0.09);
}

.info-row {
  display: flex;
  margin-bottom: 12px;
  align-items: flex-start;
  position: relative;
  z-index: 2;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-row strong {
  color: rgba(255, 255, 255, 0.9);
  min-width: 80px;
  font-size: 14px;
}

.info-row span {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  flex: 1;
}

.participant-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.participant-item {
  background: rgba(255, 255, 255, 0.06);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  padding: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.participant-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(120deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
}

.participant-item:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.participant-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  background: rgba(255, 255, 255, 0.09);
}

.participant-item i {
  color: #07D3F0;
  font-size: 16px;
  position: relative;
  z-index: 2;
}

.participant-item span {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  position: relative;
  z-index: 2;
}

/* 日程安排样式 */
.schedule-day {
  margin-bottom: 20px;
}

.schedule-day h4 {
  color: #07D3F0;
  font-size: 14px;
  margin-bottom: 10px;
  padding: 8px 12px;
  background: rgba(7, 211, 240, 0.1);
  border-radius: 8px;
}

.schedule-list {
  background: rgba(255, 255, 255, 0.06);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  padding: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.schedule-list::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(120deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
}

.schedule-list:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.schedule-item {
  display: flex;
  padding: 10px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  position: relative;
  z-index: 2;
}

.schedule-item:last-child {
  border-bottom: none;
}

.schedule-item .time {
  color: #07D3F0;
  font-size: 12px;
  font-weight: 500;
  min-width: 80px;
}

.schedule-item .content {
  color: rgba(255, 255, 255, 0.9);
  font-size: 13px;
  flex: 1;
}

.schedule-item .topic {
  margin-left: 5px;
  font-weight: 500;
  margin-bottom: 4px;
  color: rgba(255, 255, 255, 0.9);
}

/* 无数据样式 */
.no-data {
  text-align: center;
  padding: 40px 20px;
  color: rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.06);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.no-data i {
  font-size: 48px;
  color: rgba(7, 211, 240, 0.3);
  margin-bottom: 15px;
}

.no-data p {
  font-size: 14px;
  margin: 0;
}

/* 地点卡片样式 */
.location-card {
  background: rgba(255, 255, 255, 0.06);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-left: 4px solid rgba(7, 211, 240, 0.7);
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.location-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(120deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
}

.location-card:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.location-card:hover {
  transform: translateX(3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  background: rgba(255, 255, 255, 0.09);
}

.location-info h4 {
  color: rgba(255, 255, 255, 0.9);
  font-size: 16px;
  margin-bottom: 8px;
  position: relative;
  z-index: 2;
}

.location-info p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 13px;
  margin: 3px 0;
  position: relative;
  z-index: 2;
}

.map-btn {
  background: rgba(7, 211, 240, 0.2);
  color: white;
  border: 1px solid rgba(7, 211, 240, 0.3);
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  z-index: 2;
}

.map-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(120deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
}

.map-btn:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.map-btn:hover {
  background: rgba(7, 211, 240, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(7, 211, 240, 0.2);
}

/* 交通选项样式 */
.transport-options {
  background: rgba(255, 255, 255, 0.06);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  padding: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.transport-options::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(120deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
}

.transport-options:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.transport-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 15px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  position: relative;
  z-index: 2;
}

.transport-item:last-child {
  border-bottom: none;
}

.transport-item i {
  color: #07D3F0;
  font-size: 18px;
  margin-top: 2px;
}

.transport-item strong {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  display: block;
  margin-bottom: 3px;
}

.transport-item p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  margin: 0;
}

/* 酒店列表样式 */
.hotel-list {
  background: rgba(255, 255, 255, 0.06);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  padding: 0;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.hotel-list::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(120deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
}

.hotel-list:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.hotel-item {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  padding: 15px;
  margin: 0 10px 10px;
  position: relative;
  z-index: 2;
}

.hotel-item:last-child {
  margin-bottom: 10px;
}

.hotel-item h4 {
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 8px;
}

.hotel-item p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  margin: 3px 0;
}

/* 会议须知样式 */
.rules-list {
  background: rgba(255, 255, 255, 0.06);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  padding: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.rules-list::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(120deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
}

.rules-list:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.rule-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 15px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  position: relative;
  z-index: 2;
}

.rule-item:last-child {
  border-bottom: none;
}

.rule-item i {
  color: #07D3F0;
  font-size: 18px;
  margin-top: 2px;
}

.rule-item strong {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  display: block;
  margin-bottom: 3px;
}

.rule-item p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  margin: 0;
}

/* 紧急联系样式 */
.contact-emergency {
  background: rgba(255, 255, 255, 0.06);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-left: 4px solid rgba(7, 211, 240, 0.7);
  position: relative;
  overflow: hidden;
}

.contact-emergency::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(120deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
}

.contact-emergency:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.contact-emergency:hover {
  transform: translateX(3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  background: rgba(255, 255, 255, 0.09);
}

.emergency-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  position: relative;
  z-index: 2;
}

.emergency-item:last-child {
  border-bottom: none;
}

.emergency-item strong {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
}

.emergency-item span {
  color: #07D3F0;
  font-size: 14px;
  font-weight: 500;
}

/* 下载按钮样式 */
.download-section {
  text-align: center;
  margin-top: auto;
  padding: 15px 0;
}

.submit-btn {
  width: 100%;
  background: linear-gradient(135deg, rgba(7, 211, 240, 0.3), rgba(7, 211, 240, 0.15));
  color: white;
  border: 1px solid rgba(7, 211, 240, 0.3);
  border-radius: 10px;
  padding: 14px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  backdrop-filter: blur(8px);
  position: relative;
  overflow: hidden;
}

.submit-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(120deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
}

.submit-btn:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(7, 211, 240, 0.2);
  background: linear-gradient(135deg, rgba(7, 211, 240, 0.4), rgba(7, 211, 240, 0.2));
}

/* 动画效果 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInUp {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes shimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .list-container {
    padding: 15px;
  }

  .guide-tab {
    font-size: 13px;
    padding: 8px;
  }

  .location-card {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .participant-list {
    grid-template-columns: 1fr;
  }
}
</style>
