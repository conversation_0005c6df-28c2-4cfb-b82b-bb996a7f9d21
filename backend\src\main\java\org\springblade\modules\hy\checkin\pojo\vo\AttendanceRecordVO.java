/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.hy.checkin.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.modules.hy.checkin.pojo.entity.AttendanceRecordEntity;

import java.io.Serial;

/**
 * 参会签到记录表 视图实体类
 *
 * <AUTHOR>
 * @since 2025-08-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "AttendanceRecordVO对象")
public class AttendanceRecordVO extends AttendanceRecordEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 用户姓名
	 */
	@Schema(description = "用户姓名")
	private String userName;

	/**
	 * 议程主题
	 */
	@Schema(description = "议程主题")
	private String agendaTopic;

	/**
	 * 会场名称
	 */
	@Schema(description = "会场名称")
	private String venue;

}
