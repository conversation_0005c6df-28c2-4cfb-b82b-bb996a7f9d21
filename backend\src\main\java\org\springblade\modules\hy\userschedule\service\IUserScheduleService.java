/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.hy.userschedule.service;

import org.springblade.core.mp.base.BaseService;
import org.springblade.modules.hy.userschedule.pojo.entity.UserScheduleEntity;
import org.springblade.modules.hy.userschedule.pojo.vo.UserScheduleVO;
import org.springblade.modules.hy.userschedule.pojo.vo.UserComprehensiveInfoVO;
import org.springblade.modules.hy.userschedule.pojo.excel.UserScheduleExcel;
import org.springblade.modules.hy.userschedule.pojo.excel.UserScheduleImportExcel;
import org.springblade.modules.hy.userschedule.pojo.dto.UserScheduleImportResultDTO;
import org.springframework.web.multipart.MultipartFile;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import java.util.List;

/**
 * 用户日程信息表 服务类
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
public interface IUserScheduleService extends BaseService<UserScheduleEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param userSchedule
	 * @return
	 */
	IPage<UserScheduleVO> selectUserSchedulePage(IPage<UserScheduleVO> page, UserScheduleVO userSchedule);

	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<UserScheduleExcel> exportUserSchedule(Wrapper<UserScheduleEntity> queryWrapper);

	/**
	 * 根据用户ID查询用户日程信息
	 *
	 * @param userId
	 * @return
	 */
	UserScheduleVO getByUserId(Long userId);

	/**
	 * 保存或更新用户日程信息
	 *
	 * @param userSchedule
	 * @return
	 */
	boolean saveOrUpdateByUserId(UserScheduleEntity userSchedule);

	/**
	 * Excel导入用户日程信息
	 *
	 * @param file Excel文件
	 * @return 导入结果
	 */
	UserScheduleImportResultDTO importUserSchedule(MultipartFile file);

	/**
	 * 下载导入模板
	 *
	 * @return 模板数据
	 */
	List<UserScheduleImportExcel> getImportTemplate();

	/**
	 * 根据用户ID获取用户综合信息
	 *
	 * @param userId 用户ID
	 * @return 用户综合信息
	 */
	UserComprehensiveInfoVO getUserComprehensiveInfo(Long userId);

	/**
	 * 分页查询用户综合信息
	 *
	 * @param page 分页参数
	 * @param userSchedule 查询条件
	 * @return 分页结果
	 */
	IPage<UserComprehensiveInfoVO> selectUserComprehensiveInfoPage(IPage<UserComprehensiveInfoVO> page, UserScheduleVO userSchedule);

}
