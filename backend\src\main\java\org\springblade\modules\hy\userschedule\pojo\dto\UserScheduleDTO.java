/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chi<PERSON> (<EMAIL>)
 */
package org.springblade.modules.hy.userschedule.pojo.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;

/**
 * 用户日程信息表 数据传输对象
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@Data
@Schema(description = "UserScheduleDTO对象")
public class UserScheduleDTO {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	@Schema(description = "主键ID")
	private Long id;

	/**
	 * 用户ID
	 */
	@Schema(description = "用户ID")
	private Long userId;

	/**
	 * 日程信息（富文本）
	 */
//	@Schema(description = "日程信息（富文本）")
//	private String scheduleContent;

	/**
	 * 用餐信息（JSON格式）
	 */
//	@Schema(description = "用餐信息（JSON格式）")
//	private String diningInfo;

	/**
	 * 住宿信息（JSON格式）
	 */
//	@Schema(description = "住宿信息（JSON格式）")
//	private String accommodationInfo;

	/**
	 * 用户真实姓名（查询用）
	 */
	@Schema(description = "用户真实姓名（查询用）")
	private String userRealName;

	/**
	 * 用户手机号
	 */
	@Schema(description = "用户手机号")
	private String userPhone;

	/**
	 * 用户工号
	 */
	@Schema(description = "用户工号")
	private String userEmployeeNumber;

	/**
	 * 用户房号
	 */
	@Schema(description = "用户房号")
	private String userRoomNumber;

	/**
	 * 用户会议座位号
	 */
	@Schema(description = "用户会议座位号")
	private String userMeetingSeatNumber;

	/**
	 * 酒店id
	 */
	@Schema(description = "酒店id")
	private Long hotelId;

	/**
	 * 房号
	 */
	@Schema(description = "房号")
	private String roomNumber;

	/**
	 * 会议座位号
	 */
	@Schema(description = "会议座位号")
	private String meetingSeatNumber;

}
