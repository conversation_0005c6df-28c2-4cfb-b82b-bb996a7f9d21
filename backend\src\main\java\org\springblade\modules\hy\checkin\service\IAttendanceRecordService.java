/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.hy.checkin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springblade.modules.hy.checkin.pojo.dto.CheckinRequestDTO;
import org.springblade.modules.hy.checkin.pojo.dto.QRCodeResponseDTO;
import org.springblade.modules.hy.checkin.pojo.entity.AttendanceRecordEntity;
import org.springblade.modules.hy.checkin.pojo.vo.AttendanceRecordVO;

/**
 * 参会签到记录表 服务类
 *
 * <AUTHOR>
 * @since 2025-08-08
 */
public interface IAttendanceRecordService extends IService<AttendanceRecordEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page             分页对象
	 * @param attendanceRecord 查询条件
	 * @return 分页结果
	 */
	IPage<AttendanceRecordVO> selectAttendanceRecordPage(IPage<AttendanceRecordVO> page, AttendanceRecordVO attendanceRecord);

	/**
	 * 生成议程签到二维码
	 *
	 * @param agendaId 议程ID
	 * @return 二维码响应数据
	 */
	QRCodeResponseDTO generateAgendaQRCode(Long agendaId);

	/**
	 * 议程签到
	 *
	 * @param agendaId 议程ID
	 * @param userId   当前用户ID
	 * @return 签到结果
	 */
	AttendanceRecordEntity checkinByAgenda(Long agendaId, Long userId);

	/**
	 * 查询用户签到状态
	 *
	 * @param userId   用户ID
	 * @param agendaId 议程ID（可选）
	 * @return 签到记录
	 */
	AttendanceRecordEntity getCheckinStatus(Long userId, Long agendaId);

	/**
	 * 检查是否已签到
	 *
	 * @param userId   用户ID
	 * @param agendaId 议程ID（可选）
	 * @return 是否已签到
	 */
	boolean isCheckedIn(Long userId, Long agendaId);

}
