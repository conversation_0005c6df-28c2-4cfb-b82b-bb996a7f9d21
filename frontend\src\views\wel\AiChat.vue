<template>
  <div class="page-container">
    <main class="page-content">
      <div class="list-container">
        <!-- 固定标题区域 -->
        <div class="form-header">
          <i class="fas fa-robot"></i>
          <h2>会务助手</h2>
          <p>AI智能问答，为您解答会务相关问题</p>
        </div>

        <!-- 可滚动主体区域 -->
        <div class="scrollable-content">
          <!-- 快捷问题 -->
          <div class="quick-questions">
            <h3>常见问题</h3>

            <!-- 加载状态 -->
            <div v-if="isLoading" class="loading-state">
              <i class="fas fa-spinner fa-spin"></i>
              <span>正在加载常见问题...</span>
            </div>

            <!-- 错误提示 -->
            <div v-else-if="hasError" class="error-state">
              <i class="fas fa-exclamation-triangle"></i>
              <span>{{ errorMessage }}</span>
              <button class="retry-btn" @click="loadQuickQuestions">重试</button>
            </div>

            <!-- 问题列表 -->
            <div v-else class="question-grid">
              <button v-for="question in quickQuestions" :key="question" class="question-btn" @click="askQuestion(question)">
                {{ question }}
              </button>
            </div>
          </div>

          <!-- 聊天区域 -->
          <div class="chat-area" id="chatArea" ref="chatArea">
            <div v-for="message in messages" :key="message.id" class="chat-message" :class="message.type">
              <div v-if="message.type === 'system'" class="system-message">
                <i class="fas fa-info-circle"></i>
                <span>{{ message.content }}</span>
              </div>
              <div v-else-if="message.type === 'user'" class="user-message">
                <div class="message-content">{{ message.content }}</div>
                <div class="message-avatar">
                  <i class="fas fa-user"></i>
                </div>
              </div>
              <div v-else class="ai-message">
                <div class="message-avatar">
                  <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">{{ message.content }}</div>
              </div>
            </div>

            <!-- 打字指示器 -->
            <div v-if="isTyping" class="typing-indicator">
              <div class="message-avatar">
                <i class="fas fa-robot"></i>
              </div>
              <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        </div>

        <!-- 固定在底部的输入区域 -->
        <div class="input-area">
          <div class="input-container">
            <input
                v-model="currentMessage"
                type="text"
                placeholder="请输入您的问题..."
                @keypress="handleKeyPress"
                :disabled="isTyping"
            >
            <button @click="sendMessage" :disabled="isTyping || !currentMessage.trim()">
              <i class="fas fa-paper-plane"></i>
            </button>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import { getDictionary } from '@/api/system/dictbiz';

export default {
  name: 'AiChat',
  data() {
    return {
      currentMessage: '',
      isTyping: false,
      messages: [
        { id: 1, type: 'system', content: '欢迎使用会务助手！我可以为您解答会议相关问题。' }
      ],
      quickQuestions: [],
      isLoading: false,
      hasError: false,
      errorMessage: '',
      // 默认快捷问题（作为兜底数据）
      defaultQuickQuestions: [
        '会议时间安排',
        '会议地点在哪',
        '如何签到',
        '用餐安排',
        '住宿信息',
        '联系方式'
      ],
      responses: {
        '会议时间安排': '会议时间为2025年9月15日-16日，共两天。第一天08:30开始签到，09:00正式开始。详细议程请查看"议程"页面。',
        '会议地点在哪': '会议地点在广东烟草大厦会议中心，地址：广州市天河区珠江新城。可乘坐地铁3号线/5号线到珠江新城站。',
        '如何签到': '您可以通过扫码签到或手动签到两种方式。建议提前15分钟到达会场，在签到页面完成签到流程。',
        '用餐安排': '会议期间提供工作餐，午餐时间为12:00-13:30。如有特殊饮食要求，请提前联系会务组。',
        '住宿信息': '推荐住宿：广州大酒店（步行3分钟）、珠江宾馆（步行8分钟）。详细信息请查看"参会指南"页面。',
        '联系方式': '会务组联系人：张先生 138-0000-0000，技术支持：李女士 139-0000-0000。如有紧急情况请及时联系。'
      }
    }
  },
  async mounted() {
    await this.loadQuickQuestions();
  },
  methods: {
    /**
     * 加载快捷问题数据
     */
    async loadQuickQuestions() {
      this.isLoading = true;
      this.hasError = false;
      this.errorMessage = '';

      try {
        console.log('开始加载快捷问题数据...');

        // 调用字典API获取快捷问题
        const response = await getDictionary({
          code: 'hy_aichat_question' // 字典编码，需要在后台配置
        });

        console.log('快捷问题API响应:', response);

        // 检查响应格式
        if (response && response.data && response.data.success) {
          const dictData = response.data.data;

          if (dictData && Array.isArray(dictData) && dictData.length > 0) {
            // 从字典数据中提取问题文本
            this.quickQuestions = dictData.map(item => item.dictValue || item.label || item.text);
            console.log('从API加载的快捷问题:', this.quickQuestions);
          } else {
            console.log('API返回数据为空，使用默认快捷问题');
            this.quickQuestions = [...this.defaultQuickQuestions];
          }
        } else {
          throw new Error('API响应格式不正确');
        }

      } catch (error) {
        console.error('加载快捷问题失败:', error);
        this.hasError = true;
        this.errorMessage = `加载快捷问题失败: ${error.message}`;

        // 使用默认数据作为兜底
        this.quickQuestions = [...this.defaultQuickQuestions];
        console.log('使用默认快捷问题作为兜底');
      } finally {
        this.isLoading = false;
      }
    },

    askQuestion(question) {
      this.sendMessage(question);
    },
    handleKeyPress(event) {
      if (event.key === 'Enter') {
        this.sendMessage();
      }
    },
    sendMessage(predefinedMessage = null) {
      const message = predefinedMessage || this.currentMessage.trim();
      if (!message) return;

      // 添加用户消息
      this.addMessage(message, 'user');

      // 清空输入框
      if (!predefinedMessage) {
        this.currentMessage = '';
      }

      // 显示打字指示器
      this.showTypingIndicator();

      // 模拟AI回复
      setTimeout(() => {
        this.hideTypingIndicator();
        const response = this.getAIResponse(message);
        this.addMessage(response, 'ai');
      }, 1500);
    },
    addMessage(content, type) {
      this.messages.push({
        id: Date.now(),
        type: type,
        content: content
      });

      this.$nextTick(() => {
        this.scrollToBottom();
      });
    },
    showTypingIndicator() {
      this.isTyping = true;
      this.$nextTick(() => {
        this.scrollToBottom();
      });
    },
    hideTypingIndicator() {
      this.isTyping = false;
    },
    scrollToBottom() {
      const chatArea = this.$refs.chatArea;
      if (chatArea) {
        chatArea.scrollTop = chatArea.scrollHeight;
      }
    },
    getAIResponse(message) {
      // 检查是否有预设回答
      for (const [key, value] of Object.entries(this.responses)) {
        if (message.includes(key) || message.includes(key.replace(/\s/g, ''))) {
          return value;
        }
      }

      // 关键词匹配
      if (message.includes('时间') || message.includes('几点') || message.includes('什么时候')) {
        return this.responses['会议时间安排'];
      }

      if (message.includes('地点') || message.includes('地址') || message.includes('在哪')) {
        return this.responses['会议地点在哪'];
      }

      if (message.includes('签到') || message.includes('报到')) {
        return this.responses['如何签到'];
      }

      if (message.includes('吃饭') || message.includes('用餐') || message.includes('午餐')) {
        return this.responses['用餐安排'];
      }

      if (message.includes('住宿') || message.includes('酒店') || message.includes('宾馆')) {
        return this.responses['住宿信息'];
      }

      if (message.includes('联系') || message.includes('电话') || message.includes('咨询')) {
        return this.responses['联系方式'];
      }

      // 默认回复
      return '抱歉，我暂时无法理解您的问题。您可以尝试点击上方的常见问题，或者换个方式提问。如需人工服务，请联系会务组：138-0000-0000。';
    }
  }
}
</script>

<style scoped>
/* 页面通用样式*/
.page-container {
  width: 100%;
  max-height: 85vh;
  background-size: cover;
  position: relative;
  z-index: 1;
  margin: 0 auto;
  box-sizing: border-box;
}

.page-content {
  margin-top: 15px;
}

/* 容器样式 - 统一磨砂玻璃效果 */
.list-container {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(12px);
  border-radius: 15px;
  padding: 10px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: inset 0 0 10px rgba(255, 255, 255, 0.05),
  0 10px 25px rgba(0, 0, 0, 0.3);
  animation: slideInUp 0.6s ease forwards;
  max-width: 1400px;
  margin: 0 auto;
  max-height: 85vh;
  display: flex;
  flex-direction: column;
}

/* 可滚动内容容器 */
.scrollable-content {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding-right: 5px;
  margin-bottom: 15px;
  max-height: calc(85vh - 260px);
}

/* 滚动条美化 */
.scrollable-content::-webkit-scrollbar {
  width: 5px;
}

.scrollable-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
}

.scrollable-content::-webkit-scrollbar-thumb {
  background: rgba(7, 211, 240, 0.5);
  border-radius: 10px;
}

.scrollable-content::-webkit-scrollbar-thumb:hover {
  background: rgba(7, 211, 240, 0.8);
}

/* 标题区域样式统一 */
.form-header {
  text-align: center;
  margin-bottom: 25px;
  color: #ffffff;
}

.form-header i {
  font-size: 48px;
  color: #07D3F0;
  margin-bottom: 10px;
  text-shadow: 0 0 15px rgba(7, 211, 240, 0.6);
}

.form-header h2 {
  color: #ffffff;
  font-size: 24px;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.form-header p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  padding: 0 10px;
}

/* 快捷问题区域 */
.quick-questions {
  margin-bottom: 15px;
}

.quick-questions h3 {
  color: #ffffff;
  font-size: 16px;
  margin-bottom: 10px;
  text-align: center;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.question-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.question-btn {
  background: rgba(255, 255, 255, 0.06);
  border: 1px solid rgba(7, 211, 240, 0.3);
  color: white;
  padding: 10px;
  border-radius: 8px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  backdrop-filter: blur(5px);
  animation: slideInUp 0.6s ease forwards;
  position: relative;
  overflow: hidden;
}

/* 按钮悬停动画效果统一 */
.question-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(
      120deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.2) 50%,
      rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.question-btn:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.question-btn:hover {
  background: #07D3F0;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(7, 211, 240, 0.2);
}

/* 加载状态样式 */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 20px;
  color: #07D3F0;
  font-size: 14px;
}

.loading-state i {
  font-size: 16px;
}

/* 错误状态样式 */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  padding: 20px;
  color: #ff6b6b;
  font-size: 14px;
  text-align: center;
}

.error-state i {
  font-size: 20px;
  margin-bottom: 5px;
}

.retry-btn {
  background: rgba(255, 107, 107, 0.2);
  color: #ff6b6b;
  border: 1px solid rgba(255, 107, 107, 0.4);
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

.retry-btn:hover {
  background: #ff6b6b;
  color: white;
  transform: translateY(-1px);
}

/* 聊天区域 - 取消固定高度，由内容决定 */
.chat-area {
  overflow-y: auto;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  margin-bottom: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  max-height: 300px; /* 恢复与参考页面相同的固定最大高度 */
}

/* 聊天消息样式 */
.chat-message {
  margin-bottom: 15px;
  animation: slideInUp 0.3s ease forwards;
}

/* 系统消息样式 */
.system-message {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background: rgba(7, 211, 240, 0.15);
  color: white;
  padding: 10px;
  border-radius: 8px;
  font-size: 13px;
  text-align: center;
  border: 1px solid rgba(7, 211, 240, 0.2);
}

/* 用户消息样式 */
.user-message {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  gap: 10px;
}

/* AI消息样式 */
.ai-message {
  display: flex;
  justify-content: flex-start;
  align-items: flex-end;
  gap: 10px;
}

/* 头像样式统一 */
.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  flex-shrink: 0;
}

.user-message .message-avatar {
  background: rgba(7, 211, 240, 0.2);
  color: #07D3F0;
  border: 1px solid rgba(7, 211, 240, 0.3);
}

.ai-message .message-avatar {
  background: rgba(255, 255, 255, 0.1);
  color: #07D3F0;
  border: 1px solid rgba(7, 211, 240, 0.2);
}

/* 消息内容样式 */
.message-content {
  max-width: 70%;
  padding: 12px 15px;
  border-radius: 18px;
  font-size: 14px;
  line-height: 1.4;
  word-wrap: break-word;
  position: relative;
  z-index: 2;
}

.user-message .message-content {
  background: rgba(7, 211, 240, 0.8);
  color: white;
  border-bottom-right-radius: 5px;
  box-shadow: 0 2px 10px rgba(7, 211, 240, 0.2);
}

.ai-message .message-content {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-bottom-left-radius: 5px;
}

/* 打字指示器样式 */
.typing-indicator {
  display: flex;
  justify-content: flex-start;
  align-items: flex-end;
  gap: 10px;
  margin-bottom: 15px;
}

.typing-dots {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 18px;
  border-bottom-left-radius: 5px;
  padding: 12px 15px;
  display: flex;
  gap: 4px;
}

.typing-dots span {
  width: 6px;
  height: 6px;
  background: #07D3F0;
  border-radius: 50%;
  animation: typing 1.4s infinite;
}

.typing-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

/* 输入区域样式 - 固定在底部 */
.input-area {
  margin-top: auto;
}

.input-container {
  display: flex;
  gap: 10px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 25px;
  padding: 5px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(8px);
}

.input-container input {
  flex: 1;
  border: none;
  outline: none;
  padding: 12px 15px;
  font-size: 14px;
  background: transparent;
  color: rgba(255, 255, 255, 0.9);
}

.input-container input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.input-container button {
  background: #07D3F0;
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

/* 按钮悬停效果统一 */
.input-container button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(
      120deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.2) 50%,
      rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
}

.input-container button:hover:not(:disabled)::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.input-container button:hover:not(:disabled) {
  transform: scale(1.1);
  box-shadow: 0 5px 15px rgba(7, 211, 240, 0.3);
}

.input-container button:disabled {
  background: rgba(7, 211, 240, 0.3);
  cursor: not-allowed;
  transform: none;
}

/* 动画效果统一 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.5;
  }
  30% {
    transform: translateY(-10px);
    opacity: 1;
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .page-container {
    padding: 15px;
  }

  .form-header h2 {
    font-size: 20px;
  }

  .question-btn {
    font-size: 11px;
    padding: 8px;
  }
}

/* 超宽屏幕适配 */
@media (min-width: 1600px) {
  .list-container {
    padding: 30px;
  }

  .message-content {
    font-size: 15px;
  }
}
</style>
