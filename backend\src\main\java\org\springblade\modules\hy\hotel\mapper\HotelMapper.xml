<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.hy.hotel.mapper.HotelMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="hotelResultMap" type="org.springblade.modules.hy.hotel.pojo.entity.HotelEntity">
        <result column="id" property="id"/>
        <result column="hotel_name" property="hotelName"/>
        <result column="hotel_rating" property="hotelRating"/>
        <result column="hotel_address" property="hotelAddress"/>
        <result column="distance_to_venue" property="distanceToVenue"/>
        <result column="hotel_phone" property="hotelPhone"/>
        <result column="room_type" property="roomType"/>
        <result column="room_features" property="roomFeatures"/>
        <result column="checkin_time" property="checkinTime"/>
        <result column="checkout_time" property="checkoutTime"/>
        <result column="stay_days" property="stayDays"/>
        <result column="hotel_services" property="hotelServices"/>
        <result column="transport_info" property="transportInfo"/>
        <result column="contact_info" property="contactInfo"/>
        <result column="hotel_tips" property="hotelTips"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectHotelPage" resultMap="hotelResultMap">
        select * from hy_hotel where is_deleted = 0
    </select>


    <select id="exportHotel" resultType="org.springblade.modules.hy.hotel.excel.HotelExcel">
        SELECT * FROM hy_hotel ${ew.customSqlSegment}
    </select>

</mapper>
