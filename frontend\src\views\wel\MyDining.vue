<template>
  <div class="page-container">
    <!-- 页面内容 -->
    <main class="page-content">
      <div class="list-container">
        <div class="form-header">
          <i class="fas fa-utensils"></i>
          <h2>用餐安排</h2>
          <p>会议期间的餐饮服务信息</p>
        </div>

        <!-- 滚动容器 -->
        <div class="dining-scroll-container">
          <!-- 用餐状态卡片 -->
          <div class="dining-status">
            <div class="status-card list-item">
              <div class="status-header">
                <h3>今日用餐状态</h3>
                <span class="date">{{ currentDate }}</span>
              </div>
              <div class="meal-progress">
                <div v-for="meal in mealStatus" :key="meal.type" class="meal-item">
                  <div class="meal-info">
                    <i :class="meal.icon"></i>
                    <span>{{ meal.name }}</span>
                  </div>
                  <div :class="['meal-status', meal.status]">
                    <i :class="meal.statusIcon"></i>
                    {{ meal.statusText }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 餐券信息 -->
          <div class="meal-tickets">
            <h3 class="section-title">
              <i class="fas fa-ticket-alt"></i>
              我的餐券
            </h3>
            <div class="ticket-list">
              <div v-for="ticket in tickets" :key="ticket.id" :class="['ticket-item list-item', ticket.status]">
                <div class="ticket-header">
                  <div class="ticket-type">
                    <i :class="ticket.icon"></i>
                    <span>{{ ticket.type }}</span>
                  </div>
                  <div class="ticket-status">{{ ticket.statusText }}</div>
                </div>
                <div class="ticket-details">
                  <p><strong>时间：</strong>{{ ticket.time }}</p>
                  <p><strong>地点：</strong>{{ ticket.location }}</p>
                  <p v-if="ticket.menu"><strong>菜单：</strong>{{ ticket.menu }}</p>
                  <p v-if="ticket.usedTime"><strong>使用时间：</strong>{{ ticket.usedTime }}</p>
                </div>
                <button
                    v-if="ticket.status === 'available'"
                    class="use-ticket-btn submit-btn"
                    @click="useTicket(ticket.type)"
                >
                  <i class="fas fa-qrcode"></i>
                  使用餐券
                </button>
              </div>
            </div>
          </div>

          <!-- 菜单预览 -->
          <div class="menu-preview">
            <h3 class="section-title">
              <i class="fas fa-utensils"></i>
              今日菜单
            </h3>
            <div class="menu-tabs">
              <button
                  v-for="menu in menus"
                  :key="menu.type"
                  :class="['menu-tab', { active: activeMenu === menu.type }]"
                  @click="switchMenu(menu.type)"
              >
                {{ menu.name }}
              </button>
            </div>

            <div v-for="menu in menus" :key="menu.type" class="menu-content" :style="{ display: activeMenu === menu.type ? 'block' : 'none' }">
              <div class="menu-section list-item">
                <h4>{{ menuData[menu.type].title }}</h4>
                <div class="dish-list">
                  <div v-for="dish in menuData[menu.type].dishes" :key="dish.name" class="dish-item">
                    <span>{{ dish.name }}</span>
                    <small>{{ dish.description }}</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- 底部导航栏占位符 -->
    <div class="bottom-nav-placeholder"></div>
  </div>
</template>

<script>
export default {
  name: 'MyDining',
  data() {
    return {
      currentDate: '2025年9月15日',
      activeMenu: 'lunch',
      // 用餐状态
      mealStatus: [
        {
          type: 'breakfast',
          name: '早餐',
          icon: 'fas fa-coffee',
          status: 'completed',
          statusIcon: 'fas fa-check',
          statusText: '已用餐'
        },
        {
          type: 'lunch',
          name: '午餐',
          icon: 'fas fa-utensils',
          status: 'pending',
          statusIcon: 'fas fa-clock',
          statusText: '待用餐'
        },
        {
          type: 'dinner',
          name: '晚餐',
          icon: 'fas fa-moon',
          status: 'pending',
          statusIcon: 'fas fa-clock',
          statusText: '待用餐'
        }
      ],
      // 餐券信息
      tickets: [
        {
          id: 1,
          type: '早餐券',
          icon: 'fas fa-coffee',
          status: 'used',
          statusText: '已使用',
          time: '07:30 - 08:30',
          location: '大厦1楼餐厅',
          usedTime: '08:15'
        },
        {
          id: 2,
          type: '午餐券',
          icon: 'fas fa-utensils',
          status: 'available',
          statusText: '可使用',
          time: '12:00 - 13:30',
          location: '大厦1楼餐厅',
          menu: '商务套餐A'
        },
        {
          id: 3,
          type: '晚餐券',
          icon: 'fas fa-moon',
          status: 'available',
          statusText: '可使用',
          time: '18:00 - 19:30',
          location: '大厦2楼宴会厅',
          menu: '欢迎晚宴'
        }
      ],
      // 菜单信息
      menus: [
        { type: 'lunch', name: '午餐' },
        { type: 'dinner', name: '晚餐' }
      ],
      menuData: {
        lunch: {
          title: '商务套餐A',
          dishes: [
            { name: '白切鸡', description: '精选土鸡，口感鲜嫩' },
            { name: '清蒸鲈鱼', description: '新鲜鲈鱼，营养丰富' },
            { name: '时令蔬菜', description: '当季新鲜蔬菜' },
            { name: '白米饭', description: '优质大米' },
            { name: '例汤', description: '营养汤品' }
          ]
        },
        dinner: {
          title: '欢迎晚宴',
          dishes: [
            { name: '红烧狮子头', description: '传统名菜，肉质鲜美' },
            { name: '蒜蓉扇贝', description: '新鲜扇贝，蒜香浓郁' },
            { name: '宫保鸡丁', description: '经典川菜，香辣可口' },
            { name: '麻婆豆腐', description: '嫩滑豆腐，麻辣鲜香' },
            { name: '水果拼盘', description: '时令水果' }
          ]
        }
      }
    };
  },
  methods: {
    // 切换菜单
    switchMenu(menuType) {
      this.activeMenu = menuType;
    },

    // 使用餐券
    useTicket(ticketType) {
      const ticket = this.tickets.find(t => t.type.includes(ticketType === 'lunch' ? '午餐' : '晚餐'));
      if (ticket) {
        if (this.$message) {
          this.$message.success(`${ticket.type}使用成功！`);
        } else {
          alert(`${ticket.type}使用成功！`);
        }
        // 更新餐券状态
        ticket.status = 'used';
        ticket.statusText = '已使用';
        ticket.usedTime = new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });

        // 更新用餐状态
        const mealType = ticketType === 'lunch' ? 'lunch' : 'dinner';
        const meal = this.mealStatus.find(m => m.type === mealType);
        if (meal) {
          meal.status = 'completed';
          meal.statusIcon = 'fas fa-check';
          meal.statusText = '已用餐';
        }
      }
    }
  }
};
</script>

<style scoped>
/* 页面容器 */
.page-container {
  width: 100%;
  max-height: 85vh;
  background-size: cover;
  position: relative;
  z-index: 1;
  margin: 0 auto;
  box-sizing: border-box;
}

/* 页面内容 */
.page-content {
  margin-top: 15px;
}

/* 容器样式 - 统一磨砂玻璃效果 */
.list-container {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(12px);
  border-radius: 15px;
  padding: 10px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: inset 0 0 10px rgba(255, 255, 255, 0.05),
  0 10px 25px rgba(0, 0, 0, 0.3);
  animation: slideInUp 0.6s ease forwards;
  max-width: 1400px;
  margin: 0 auto;
  max-height: 85vh;
}

/* 滚动容器 - 与其他页面保持一致 */
.dining-scroll-container {
  max-height: calc(85vh - 180px);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding-right: 5px;
}

/* 滚动条美化 - 统一风格 */
.dining-scroll-container::-webkit-scrollbar {
  width: 5px;
}

.dining-scroll-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
}

.dining-scroll-container::-webkit-scrollbar-thumb {
  background: rgba(7, 211, 240, 0.5);
  border-radius: 10px;
}

.dining-scroll-container::-webkit-scrollbar-thumb:hover {
  background: rgba(7, 211, 240, 0.8);
}

/* 标题区域样式统一 */
.form-header {
  text-align: center;
  margin-bottom: 25px;
  color: #ffffff;
}

.form-header i {
  font-size: 36px;
  color: #07D3F0;
  margin-bottom: 10px;
  text-shadow: 0 0 15px rgba(7, 211, 240, 0.6);
}

.form-header h2 {
  color: #ffffff;
  font-size: 24px;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.form-header p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  padding: 0 10px;
}

/* 区域标题样式 - 与其他页面统一 */
.section-title {
  background: linear-gradient(135deg, rgba(7, 211, 240, 0.3), rgba(7, 211, 240, 0.15));
  color: white;
  padding: 15px 20px;
  border-radius: 10px;
  margin-bottom: 20px;
  font-size: 17px;
  display: flex;
  align-items: center;
  gap: 10px;
  border: 1px solid rgba(7, 211, 240, 0.2);
  backdrop-filter: blur(8px);
}

/* 列表项样式 - 卡片风格 */
.list-item {
  background: rgba(255, 255, 255, 0.06);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-left: 4px solid rgba(7, 211, 240, 0.7);
  transition: all 0.3s ease;
  animation: slideInUp 0.6s ease forwards;
  position: relative;
  overflow: hidden;
}

/* 卡片悬停效果 */
.list-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(
      120deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.2) 50%,
      rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.list-item:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.list-item:hover {
  transform: translateX(3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  background: rgba(255, 255, 255, 0.09);
}

/* 用餐状态 */
.dining-status {
  margin: 20px 0;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.status-header h3 {
  color: #ffffff;
  font-size: 16px;
  margin: 0;
}

.status-header .date {
  color: #07D3F0;
  font-size: 12px;
  font-weight: 500;
}

.meal-progress {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.meal-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
}

.meal-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.meal-info i {
  color: #07D3F0;
  font-size: 16px;
}

.meal-info span {
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
}

.meal-status {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 12px;
}

.meal-status.completed {
  background: rgba(7, 211, 240, 0.2);
  color: #07D3F0;
}

.meal-status.pending {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

/* 餐券信息 */
.meal-tickets {
  margin: 20px 0;
}

.ticket-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.ticket-item.used {
  border-left-color: rgba(255, 255, 255, 0.3);
  opacity: 0.8;
}

.ticket-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.ticket-type {
  display: flex;
  align-items: center;
  gap: 8px;
}

.ticket-type i {
  color: #07D3F0;
  font-size: 16px;
}

.ticket-type span {
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
}

.ticket-status {
  padding: 4px 8px;
  border-radius: 10px;
  font-size: 12px;
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

.ticket-item.used .ticket-status {
  background: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.6);
}

.ticket-item.available .ticket-status {
  background: rgba(7, 211, 240, 0.2);
  color: #07D3F0;
}

.ticket-details {
  margin-bottom: 15px;
}

.ticket-details p {
  margin: 5px 0;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

.ticket-details strong {
  color: #07D3F0;
}

/* 菜单预览 */
.menu-preview {
  margin: 20px 0;
}

.menu-tabs {
  display: flex;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 5px;
  margin-bottom: 20px;
}

.menu-tab {
  flex: 1;
  background: none;
  border: none;
  padding: 10px;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: rgba(255, 255, 255, 0.7);
}

.menu-tab.active {
  background: rgba(7, 211, 240, 0.2);
  color: white;
}

.menu-section h4 {
  color: #ffffff;
  font-size: 16px;
  margin-bottom: 15px;
  text-align: center;
  padding-bottom: 10px;
  border-bottom: 2px solid rgba(7, 211, 240, 0.7);
}

.dish-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.dish-item {
  display: flex;
  flex-direction: column;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.dish-item span {
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 3px;
}

.dish-item small {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
}

/* 按钮样式统一 */
.submit-btn {
  width: 100%;
  background: linear-gradient(135deg, rgba(7, 211, 240, 0.3), rgba(7, 211, 240, 0.15));
  color: white;
  border: 1px solid rgba(7, 211, 240, 0.3);
  border-radius: 10px;
  padding: 14px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  backdrop-filter: blur(8px);
  position: relative;
  overflow: hidden;
}

.submit-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(
      120deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.2) 50%,
      rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
}

.submit-btn:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(7, 211, 240, 0.2);
  background: linear-gradient(135deg, rgba(7, 211, 240, 0.4), rgba(7, 211, 240, 0.2));
}

/* 底部导航栏占位符 */
.bottom-nav-placeholder {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 10vh;
  z-index: 10;
  pointer-events: none;
}

/* 动画效果统一 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .list-container {
    padding: 15px;
  }

  .form-header i {
    font-size: 40px;
  }

  .form-header h2 {
    font-size: 20px;
  }

  .dining-scroll-container {
    max-height: calc(85vh - 180px);
  }
}
</style>